<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">
    <title>{{ $fileName }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0mm;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 10px;
            }

            h1 {
                font-size: 16px !important;
            }

            th,
            td {
                font-size: 10px !important;
            }

            .print-grid {
                display: grid !important;
                grid-template-columns: 1fr 1fr !important;
                gap: 1rem;
            }

            th,
            tfoot tr {
                background-color: #f9fafb !important;
                /* <PERSON>l<PERSON>'s gray-50 */
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            * {
                border-color: #6b7280 !important;
                /* gray-500 */
                border-radius: 2px !important;
                /* small radius */
            }

        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        {{-- HEADER --}}
        <x-document-header :collegeLogo="asset('images/racoed-favicon.png')" :collegeName="$collegeSettings->name"
            :collegeMotto="$collegeSettings->motto" :collegeAddress="$collegeSettings->address"
            :collegePhone="$collegeSettings->phone" :collegeEmail="$collegeSettings->email"
            :studentPhoto="$student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')"
            heading="Examination & Records" subheading="Course Registration" />

        {{-- STUDENT & ACADEMIC DETAILS --}}
        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Student & Academic Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-1">
                <div class="border p-0.5"><strong>Name:</strong> {{ $student->name ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Session:</strong> {{ $registration?->schoolSession->name ?? 'NIL' }}
                </div>
                <div class="border p-0.5 "><strong>Matric. no.:</strong> {{ $student->matric_number ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Level:</strong> {{ $registration?->level->name ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Phone:</strong> {{ $student->phone ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Semester:</strong> {{ $registration?->semester->name ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Gender:</strong> {{ $student->gender ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Programme:</strong> {{ $registration?->programme->name ?? 'NIL' }}
                </div>
            </div>
        </div>

        {{-- REGISTERED COURSES --}}
        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Registered Courses</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-1 text-xs print-grid">
                @foreach ($coursesByDepartment as $departmentData)
                <div>
                    <h3 class="font-semibold text-center mb-1">{{ $departmentData['department']->name }} Courses</h3>
                    <table class="w-full border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border px-2 py-1 text-left">Code</th>
                                <th class="border px-2 py-1 text-left">Title</th>
                                <th class="border px-2 py-1 text-center">Credit</th>
                                <th class="border px-2 py-1 text-center">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($departmentData['courses'] as $course)
                            <tr>
                                <td class="border px-2 py-0.5 whitespace-nowrap">{{ $course->code }}</td>
                                <td class="border px-2 py-0.5">{{ $course->title }}</td>
                                <td class="border px-2 py-0.5 text-center">{{ $course->credit }}</td>
                                <td class="border px-2 py-0.5 text-center">{{ $course->course_status->getAlias() }}</td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="4" class="border px-2 py-2 text-center">No courses</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                @endforeach
            </div>

        </div>

        {{-- SIGNATURES --}}
        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Student & Head of Departments (H.O.Ds) Signature</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-1 text-xs">
                {{-- Student Signature --}}
                <div class="border p-1 ">
                    <p><span class="font-semibold">Student</span></p>
                    <p><span class="font-semibold">Name: </span>____________________________________</p>
                    <p><span class="font-semibold">Sign & Date: </span> _____________________________</p>
                </div>
                @foreach ($departmentsWithHeads as $dept)
                <div class="border p-1">
                    <p><span class="font-semibold">Dept: </span>{{ $dept->name }}</p>
                    <p>
                        <span class="font-semibold">Name: </span>
                        @if ($dept->headOfDepartments->isNotEmpty())
                        @php $hod = $dept->headOfDepartments->first(); @endphp
                        {{ $hod->title?->getLabel() }} {{ $hod->name }}
                        {{-- @if ($hod->qualification)
                        ({{ collect($hod->qualification)
                        ->map(fn($q) => \App\Enums\Qualification::from($q)->getLabel())
                        ->join(', ') }})
                        @endif--}}
                        @else
                        ____________________________________
                        @endif
                    </p>
                    <p><span class="font-semibold">Sign & Date: </span> _____________________________</p>
                </div>
                @endforeach
            </div>
        </div>

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print registration
            </x-filament::button>
        </div>
    </div>

    <script>
        window.onload = function() {
                window.print();
            }
    </script>
    @livewireScripts
    @filamentScripts
</body>

</html>