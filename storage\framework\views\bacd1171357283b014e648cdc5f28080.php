<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="<?php echo e(asset('images/racoed-favicon.png')); ?>" type="image/png">
    <title><?php echo e($fileName); ?></title>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo \Filament\Support\Facades\FilamentAsset::renderStyles() ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/filament/custom/theme.css', 'resources/css/app.css']); ?>
    <style>
        @media print {
            @page {
                size: A4 landscape;
                margin: 5mm;
            }

            .college-name-print {
                font-size: 20pt !important;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 8px;
            }

            h1 {
                font-size: 16px !important;
            }

            h2 {
                font-size: 14px !important;
            }

            h3 {
                font-size: 12px !important;
            }

            th,
            td {
                font-size: 8px !important;
                padding: 2px !important;
            }

            th,
            tfoot tr {
                background-color: #f9fafb !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            * {
                border-color: #6b7280 !important;
                border-radius: 2px !important;
            }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-full mx-auto p-2 text-sm space-y-4">
        
        <?php if (isset($component)) { $__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.document-simple-header','data' => ['collegeLogo' => asset('images/racoed-favicon.png'),'collegeName' => $collegeSettings->name,'heading' => 'Examination & Records','subheading' => 'Result Overview']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('document-simple-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['collegeLogo' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(asset('images/racoed-favicon.png')),'collegeName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->name),'heading' => 'Examination & Records','subheading' => 'Result Overview']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a)): ?>
<?php $attributes = $__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a; ?>
<?php unset($__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a)): ?>
<?php $component = $__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a; ?>
<?php unset($__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a); ?>
<?php endif; ?>

        
        <div class="border p-2">
            <h2 class="text-center font-bold mb-2">Overview Details</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-1">
                <div class="border p-1"><strong>Session:</strong> <?php echo e($session ?? 'NIL'); ?></div>
                <div class="border p-1"><strong>Semester:</strong> <?php echo e($semester ?? 'NIL'); ?></div>
                <div class="border p-1"><strong>Level:</strong> <?php echo e($level ?? 'NIL'); ?></div>
                <div class="border p-1"><strong>Department:</strong> <?php echo e($department ?? 'NIL'); ?></div>
            </div>
        </div>

        
        <div class="border p-1">
            <h2 class="text-lg font-semibold mb-2 text-center">Overview Report</h2>

            <?php if (isset($component)) { $__componentOriginal5837319ee62a160ade163b4570aeaa61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5837319ee62a160ade163b4570aeaa61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.overview','data' => ['livewire' => $livewire,'filters' => $filters,'filteredRecords' => $filteredRecords,'courses' => $courses,'grades' => $grades,'failedScore' => $failedScore,'semesterTotalCreditUnit' => $semesterTotalCreditUnit,'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('overview'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['livewire' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($livewire),'filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($filters),'filteredRecords' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($filteredRecords),'courses' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($courses),'grades' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($grades),'failedScore' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($failedScore),'semesterTotalCreditUnit' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($semesterTotalCreditUnit),'cumulativeTotalCreditUnit' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($cumulativeTotalCreditUnit)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5837319ee62a160ade163b4570aeaa61)): ?>
<?php $attributes = $__attributesOriginal5837319ee62a160ade163b4570aeaa61; ?>
<?php unset($__attributesOriginal5837319ee62a160ade163b4570aeaa61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5837319ee62a160ade163b4570aeaa61)): ?>
<?php $component = $__componentOriginal5837319ee62a160ade163b4570aeaa61; ?>
<?php unset($__componentOriginal5837319ee62a160ade163b4570aeaa61); ?>
<?php endif; ?>

        </div>

        
        <div class="text-center print:hidden">
            <button onclick="window.print()"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Print Overview
            </button>
        </div>
    </div>

    <script>
        window.onload = function() {
                window.print();
            }
    </script>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <?php echo \Filament\Support\Facades\FilamentAsset::renderScripts() ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/documents/overview.blade.php ENDPATH**/ ?>